# 模型推理脚本使用指南

本文档详细介绍了如何使用 `inference.py` 脚本对训练好的模型进行推理和评估。

## 文件说明

- `inference.py`: 主要的推理脚本
- `run_inference_example.py`: 使用示例脚本
- `network_blocks.py`: 网络架构模块（与训练脚本共享）
- `dataset.py`: 数据集加载模块（与训练脚本共享）

## 功能特性

### ✅ 已实现的功能

1. **模型加载功能**
   - 支持加载指定的模型检查点文件
   - 自动检测GPU/CPU并加载到相应设备
   - 完全复用训练脚本中的模型架构

2. **数据集处理**
   - 支持用户指定的测试数据集目录
   - 与训练脚本保持一致的数据预处理流程
   - 自动查找并加载 `labels.csv` 文件

3. **预测和评估功能**
   - 对测试集进行批量预测
   - 收集真实值和预测值
   - 输出预测结果到文本文件

4. **性能指标计算**
   - MAE (Mean Absolute Error)
   - RMSE (Root Mean Squared Error)
   - Pearson相关系数和P值

5. **结果输出**
   - 控制台显示评估指标
   - 保存预测结果到 `predictions.txt`
   - 保存评估指标到 `evaluation_metrics.txt`

## 使用方法

### 方法1: 使用示例脚本（推荐）

```bash
# 运行示例脚本（使用默认参数）
python run_inference_example.py

# 查看使用说明
python run_inference_example.py --help
```

### 方法2: 直接使用推理脚本

```bash
python inference.py \
  --checkpoint ./saved_models/checkpoint_epoch_5.pth \
  --test_dataset_dir ~/my_private_on_AT1/ak-score-1-data/core-set_junsu-pdbqt/ \
  --output_dir ./inference_results \
  --batch_size 96 \
  --num_workers 8
```

## 命令行参数

| 参数 | 类型 | 必需 | 默认值 | 说明 |
|------|------|------|--------|------|
| `--checkpoint` | str | ✅ | - | 模型检查点文件路径 |
| `--test_dataset_dir` | str | ✅ | - | 测试数据集目录路径 |
| `--output_dir` | str | ❌ | `./inference_results` | 结果输出目录 |
| `--batch_size` | int | ❌ | `96` | 推理批次大小 |
| `--num_workers` | int | ❌ | `8` | 数据加载工作进程数 |

## 输入要求

### 1. 模型检查点文件
- 路径格式: `./saved_models/checkpoint_epoch_XX.pth`
- 文件必须包含训练脚本保存的完整状态字典

### 2. 测试数据集目录
- 目录必须包含 `labels.csv` 文件
- 目录必须包含对应的 `.npy` 数据文件
- 数据格式必须与训练数据一致

### 3. 数据集结构示例
```
test_dataset_dir/
├── labels.csv          # 包含文件名和标签的CSV文件
├── sample1.npy         # 3D数组数据文件
├── sample2.npy
└── ...
```

## 输出文件

### 1. predictions.txt
包含真实值和预测值的对比数据：
```
True_Value	Predicted_Value
-8.500000	-8.234567
-7.200000	-7.456789
...
```

### 2. evaluation_metrics.txt
包含详细的评估指标：
```
=== Model Evaluation Metrics ===
MAE (Mean Absolute Error): 1.234567
RMSE (Root Mean Squared Error): 1.567890
Pearson Correlation Coefficient: 0.876543
Pearson P-Value: 1.234567e-10
```

## 依赖要求

确保安装了以下Python包：
- `torch` (PyTorch)
- `numpy`
- `pandas`
- `scipy` (用于计算Pearson相关系数)

## 错误处理

脚本包含完善的错误处理机制：

1. **文件不存在错误**: 检查检查点文件和数据集文件是否存在
2. **设备兼容性**: 自动检测并适配GPU/CPU
3. **内存管理**: 使用批处理和适当的内存管理
4. **数据格式验证**: 确保数据格式与训练时一致

## 性能优化

- 使用 `pin_memory=True` 加速GPU数据传输
- 支持多进程数据加载 (`num_workers`)
- 推理时禁用梯度计算 (`torch.no_grad()`)
- 批处理推理提高效率

## 注意事项

1. **模型架构一致性**: 推理脚本的模型架构与训练脚本完全一致
2. **数据预处理一致性**: 使用相同的数据加载和预处理流程
3. **设备兼容性**: 自动处理GPU/CPU设备切换
4. **内存使用**: 大数据集推理时注意调整批次大小

## 故障排除

### 常见问题

1. **CUDA内存不足**
   - 减小 `--batch_size` 参数
   - 减少 `--num_workers` 参数

2. **找不到检查点文件**
   - 检查文件路径是否正确
   - 确认文件确实存在

3. **数据集加载失败**
   - 检查 `labels.csv` 文件是否存在
   - 确认数据文件格式正确

4. **模型加载失败**
   - 确认检查点文件完整性
   - 检查模型架构是否匹配

如有其他问题，请检查错误信息并根据提示进行调试。
