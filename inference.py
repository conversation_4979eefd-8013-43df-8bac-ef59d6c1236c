import torch
from torch import nn
from torch.nn import functional as F
from torch.utils import data

import numpy as np
import pandas as pd
import argparse
import os
import time
from scipy.stats import pearsonr

from network_blocks import SeparableConv3d, ResidualLayer, create_stacked_blocks, init_weights
from dataset import Efficient3DDataset


def create_model():
    """
    创建与训练脚本完全相同的模型架构
    """
    # 构建网络架构 - 与训练脚本完全一致
    b1 = nn.Sequential(SeparableConv3d(13, 64, kernel_size=5, stride=2, padding=2))
    stacked_RLx10 = create_stacked_blocks(10)

    net = nn.Sequential(
        b1,
        stacked_RLx10,
        nn.MaxPool3d(kernel_size=3, stride=2, padding=0),
        ResidualLayer(input_channels=64, output_channels=256),
        ResidualLayer(input_channels=256, output_channels=256),
        ResidualLayer(input_channels=256, output_channels=256),
        ResidualLayer(input_channels=256, output_channels=256),
        nn.MaxPool3d(kernel_size=3, stride=2, padding=0),
        ResidualLayer(input_channels=256, output_channels=512),
        nn.Dropout(0.2),
        nn.AdaptiveAvgPool3d(1),
        nn.Flatten(),
        nn.Linear(512, 1)
    )

    return net


def load_model(checkpoint_path, device):
    """
    加载训练好的模型检查点
    """
    print(f"Loading model from checkpoint: {checkpoint_path}")

    # 创建模型
    model = create_model()

    # 加载检查点
    if not os.path.exists(checkpoint_path):
        raise FileNotFoundError(f"Checkpoint file not found: {checkpoint_path}")

    checkpoint = torch.load(checkpoint_path, map_location=device)
    model.load_state_dict(checkpoint['model_state_dict'])

    # 移动到指定设备
    model = model.to(device)
    model.eval()  # 设置为评估模式

    print(f"Model loaded successfully from epoch {checkpoint['epoch']}")
    return model


def load_test_dataset(test_dataset_dir, batch_size, num_workers=8):
    """
    加载测试数据集
    """
    csv_file = os.path.join(os.path.expanduser(test_dataset_dir), 'labels.csv')
    root_dir = os.path.expanduser(test_dataset_dir)

    if not os.path.exists(csv_file):
        raise FileNotFoundError(f"Labels file not found: {csv_file}")

    test_dataset = Efficient3DDataset(csv_file=csv_file, root_dir=root_dir)
    print(f"Test dataset loaded successfully. Total samples: {len(test_dataset)}")

    test_loader = data.DataLoader(
        dataset=test_dataset,
        batch_size=batch_size,
        shuffle=False,  # 推理时不需要打乱
        num_workers=num_workers,
        pin_memory=True
    )

    return test_loader


def run_inference(model, test_loader, device):
    """
    运行推理，返回真实值和预测值
    """
    print("Running inference...")

    all_predictions = []
    all_targets = []

    model.eval()
    with torch.no_grad():
        for batch_idx, (X, y) in enumerate(test_loader):
            # 将数据移动到指定设备
            X, y = X.to(device), y.to(device)

            # 前向传播
            y_hat = model(X)
            y_hat = y_hat.squeeze()  # 移除维度为1的维度

            # 收集预测结果
            all_predictions.extend(y_hat.cpu().numpy())
            all_targets.extend(y.cpu().numpy())

            if (batch_idx + 1) % 10 == 0:
                print(f"Processed {batch_idx + 1} batches...")

    print(f"Inference completed. Total samples processed: {len(all_predictions)}")
    return np.array(all_targets), np.array(all_predictions)


def calculate_metrics(y_true, y_pred):
    """
    计算评估指标：MAE, RMSE, Pearson相关系数
    """
    # MAE (Mean Absolute Error)
    mae = np.mean(np.abs(y_true - y_pred))

    # RMSE (Root Mean Squared Error)
    rmse = np.sqrt(np.mean((y_true - y_pred) ** 2))

    # Pearson相关系数
    pearson_corr, pearson_p_value = pearsonr(y_true, y_pred)

    return {
        'MAE': mae,
        'RMSE': rmse,
        'Pearson_Correlation': pearson_corr,
        'Pearson_P_Value': pearson_p_value
    }


def save_predictions(y_true, y_pred, output_file):
    """
    保存预测结果到文件
    """
    print(f"Saving predictions to: {output_file}")

    # 创建输出目录（如果不存在）
    os.makedirs(os.path.dirname(output_file), exist_ok=True)

    # 保存预测结果
    with open(output_file, 'w') as f:
        f.write("True_Value\tPredicted_Value\n")
        for true_val, pred_val in zip(y_true, y_pred):
            f.write(f"{true_val:.6f}\t{pred_val:.6f}\n")

    print(f"Predictions saved successfully.")


def save_metrics(metrics, output_dir):
    """
    保存评估指标到文件
    """
    metrics_file = os.path.join(output_dir, 'evaluation_metrics.txt')

    print(f"Saving metrics to: {metrics_file}")

    with open(metrics_file, 'w') as f:
        f.write("=== Model Evaluation Metrics ===\n")
        f.write(f"MAE (Mean Absolute Error): {metrics['MAE']:.6f}\n")
        f.write(f"RMSE (Root Mean Squared Error): {metrics['RMSE']:.6f}\n")
        f.write(f"Pearson Correlation Coefficient: {metrics['Pearson_Correlation']:.6f}\n")
        f.write(f"Pearson P-Value: {metrics['Pearson_P_Value']:.6e}\n")

    print(f"Metrics saved successfully.")


def main():
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='Model Inference Script')
    parser.add_argument('--checkpoint', type=str, required=True,
                        help='Path to model checkpoint file (e.g., ./saved_models/checkpoint_epoch_XX.pth)')
    parser.add_argument('--test_dataset_dir', type=str, required=True,
                        help='Directory containing test dataset with labels.csv')
    parser.add_argument('--output_dir', type=str, default='./inference_results',
                        help='Directory to save inference results (default: ./inference_results)')
    parser.add_argument('--batch_size', type=int, default=40,
                        help='Batch size for inference (default: 40)')
    parser.add_argument('--num_workers', type=int, default=8,
                        help='Number of data loading workers (default: 8)')

    args = parser.parse_args()

    # 设备检测
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    print(f"PyTorch version: {torch.__version__}")

    try:
        # 1. 加载模型
        model = load_model(args.checkpoint, device)

        # 2. 加载测试数据集
        test_loader = load_test_dataset(args.test_dataset_dir, args.batch_size, args.num_workers)

        # 3. 运行推理
        start_time = time.time()
        y_true, y_pred = run_inference(model, test_loader, device)
        inference_time = time.time() - start_time

        print(f"Inference completed in {inference_time:.2f} seconds")

        # 4. 计算评估指标
        metrics = calculate_metrics(y_true, y_pred)

        # 5. 显示结果
        print("\n=== Evaluation Results ===")
        print(f"MAE (Mean Absolute Error): {metrics['MAE']:.6f}")
        print(f"RMSE (Root Mean Squared Error): {metrics['RMSE']:.6f}")
        print(f"Pearson Correlation Coefficient: {metrics['Pearson_Correlation']:.6f}")
        print(f"Pearson P-Value: {metrics['Pearson_P_Value']:.6e}")

        # 6. 保存结果
        os.makedirs(args.output_dir, exist_ok=True)

        # 保存预测结果
        predictions_file = os.path.join(args.output_dir, 'predictions.txt')
        save_predictions(y_true, y_pred, predictions_file)

        # 保存评估指标
        save_metrics(metrics, args.output_dir)

        print(f"\nAll results saved to: {args.output_dir}")

    except Exception as e:
        print(f"Error during inference: {str(e)}")
        raise


if __name__ == "__main__":
    main()