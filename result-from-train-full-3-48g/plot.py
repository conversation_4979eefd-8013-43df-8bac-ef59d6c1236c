# plot predicted Y vs. true Y 
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
 

inpf = 'predictions.txt'

# Read the data
data = pd.read_csv(inpf, sep='\t')
true_values = data['True_Value']
predicted_values = data['Predicted_Value']


# 采集的数据有一个特点，对于每个数据点，我们重复记录了24个值（占了24行），因此，我们需要对数据进行平均化
# 重塑数据为24个一组
n_samples = len(true_values) // 24
true_values = np.array(true_values).reshape(n_samples, 24).mean(axis=1)
predicted_values = np.array(predicted_values).reshape(n_samples, 24).mean(axis=1)


# Create the scatter plot
plt.figure(figsize=(10, 10))
plt.scatter(true_values, predicted_values, alpha=0.5)
plt.plot([true_values.min(), true_values.max()], [true_values.min(), true_values.max()], 'r--')

# Add labels and title
plt.xlabel('True Values')
plt.ylabel('Predicted Values')
plt.title('Predicted vs True Values')

# Add correlation coefficient
correlation = np.corrcoef(true_values, predicted_values)[0,1]
plt.text(0.05, 0.95, f'Correlation (R): {correlation:.3f}', 
         transform=plt.gca().transAxes)

# Add RMSE 
rmse = np.sqrt(np.mean((true_values - predicted_values) ** 2))
plt.text(0.05, 0.90, f'RMSE: {rmse:.3f}', 
         transform=plt.gca().transAxes)

# Save the plot
#plt.show()
plt.savefig('prediction_scatter.png')
plt.close()
