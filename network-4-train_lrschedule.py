import torch
from torch import nn
from torch.nn import functional as F
from torch.utils import data

import numpy as np
import pandas as pd

import os
import time
#import matplotlib.pyplot as plt

from network_blocks import SeparableConv3d, ResidualLayer, create_stacked_blocks, init_weights
from dataset import Efficient3DDataset





IF_RESUME = False

NUM_EPOCHS = 120          # 计划训练的总轮数
BATCH_SIZE = 96          # 批处理大小
LEARNING_RATE = 0.0005   # 学习率 文章 ensemble: 0.0007, single: 0.0001/5/7/10

# 文件与路径设置
SAVE_EVERY_Q_EPOCHS = 1 # 每Q个epoch保存一次checkpoint
MODEL_SAVE_DIR = "./saved_models" # checkpoint保存目录
LOG_FILE_PATH = "./logs/training_log_regression.txt" # 日志文件路径
NUM_BATCH_WORKERS = 8

RESUME_CHECKPOINT_PATH = "" # "./saved_models/checkpoint_epoch_3.pth"

TRAIN_DATASET_DIR = '~/my_private_on_AT1/ak-score-1-data/refined-set-training/'
TEST_DATASET_DIR  = '~/my_private_on_AT1/ak-score-1-data/core-set_junsu-pdbqt/'



print("torch version: ",torch.__version__)


# construct network
b1 = nn.Sequential(SeparableConv3d(13, 64, kernel_size=5, stride=2, padding=2))  # first parameter is input_channels 
                    #nn.BatchNorm3d(64), nn.ReLU())
stacked_RLx10 = create_stacked_blocks(10) # 10 

net = nn.Sequential(b1, \
                    stacked_RLx10, \
                    nn.MaxPool3d(kernel_size=3, stride=2, padding=0), \
                    ResidualLayer(input_channels=64, output_channels=256), \
                    ResidualLayer(input_channels=256, output_channels=256), \
                    ResidualLayer(input_channels=256, output_channels=256), \
                    ResidualLayer(input_channels=256, output_channels=256), \
                    nn.MaxPool3d(kernel_size=3, stride=2, padding=0), \
                    ResidualLayer(input_channels=256, output_channels=512), \
                    nn.Dropout(0.2), \
                    nn.AdaptiveAvgPool3d(1), \
                    nn.Flatten(), \
                    nn.Linear(512, 1)
                    )



# parameter initialization 
net.apply(init_weights) 



# check network
X = torch.rand(size=(1, 13, 30, 30, 30))
for layer in net:
    X = layer(X)
    print(layer.__class__.__name__,'output shape:\t', X.shape)




# load dataset 
train_dataset = Efficient3DDataset(csv_file=os.path.expanduser(TRAIN_DATASET_DIR+'labels.csv'), root_dir=os.path.expanduser(TRAIN_DATASET_DIR))
print(f"Training dataset created successfully. Total samples: {len(train_dataset)}")

train_iter = data.DataLoader(dataset=train_dataset, batch_size=BATCH_SIZE, shuffle=True, num_workers=NUM_BATCH_WORKERS, pin_memory=True) 



test_dataset = None
test_dataset = Efficient3DDataset(csv_file=os.path.expanduser(TEST_DATASET_DIR+'labels.csv'), root_dir=os.path.expanduser(TEST_DATASET_DIR))
print(f"Test dataset created successfully. Total samples: {len(test_dataset)}")

test_iter = None
if test_dataset:
    test_iter = data.DataLoader(dataset=test_dataset, batch_size=BATCH_SIZE, shuffle=False, num_workers=NUM_BATCH_WORKERS, pin_memory=True)





# move network to GPU 
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f"Using device: {device}")

net = net.to(device)


loss = nn.L1Loss()  # changed to MAE loss now
trainer = torch.optim.AdamW(net.parameters(), lr=LEARNING_RATE, betas=(0.99, 0.999), weight_decay=1e-4)

# ==============================================================================
# 2. 加载 Checkpoint 或 初始化 (Load Checkpoint or Initialize)
# ==============================================================================
start_epoch = 0
#history = { 'train_loss': [], 'test_loss': [], 'train_mae': [], 'test_mae': [] }
history = { 'train_loss': [], 'test_loss': [], 'train_mae': [], 'train_rmse': [], 'test_mae': [], 'test_rmse': [] }

# 如果指定了checkpoint路径且文件存在，则加载
if RESUME_CHECKPOINT_PATH and os.path.exists(RESUME_CHECKPOINT_PATH) and IF_RESUME:
    print(f"Resuming training from checkpoint: {RESUME_CHECKPOINT_PATH}")
    # map_location确保模型能被正确加载到当前设备(CPU/GPU)
    checkpoint = torch.load(RESUME_CHECKPOINT_PATH, map_location=device)
    
    net.load_state_dict(checkpoint['model_state_dict'])
    trainer.load_state_dict(checkpoint['optimizer_state_dict'])
    start_epoch = checkpoint['epoch']
    history = checkpoint['history']
    
    print(f"Successfully resumed. Training will continue from epoch {start_epoch + 1}.")
else:
    print("No checkpoint found or specified. Starting training from scratch.")
    # 如果从头开始，则创建新的日志文件
    os.makedirs(os.path.dirname(LOG_FILE_PATH), exist_ok=True)
    with open(LOG_FILE_PATH, 'w') as f:
        #header = "Epoch | Time per Epoch | Train Loss | Test Loss  | Train MAE  | Test MAE\n"
        header = "Epoch | Time per Epoch | Train Loss | Test Loss  | Train MAE | Train RMSE | Test MAE | Test RMSE\n"
        f.write(header)
        print(header.strip())




# ==============================================================================
# 3. 核心训练循环 (Core Training Loop)
# ==============================================================================
# 循环从 start_epoch 开始，到 NUM_EPOCHS 结束
for epoch in range(start_epoch, NUM_EPOCHS):
    #print(f"== Training: Epoch {epoch + 1}/{NUM_EPOCHS} ==")
    epoch_start_time = time.time()
    
    # --- 训练阶段 ---
    net.train() # 设置为训练模式
    train_loss_sum, train_mae_sum, train_se_sum, train_total_count = 0.0, 0.0, 0.0, 0
    for X, y in train_iter:
        # 将数据移动到指定设备
        X, y = X.to(device), y.to(device)
        
        # 前向传播
        y_hat = net(X)
        
        # 计算损失
        y_hat = y_hat.squeeze() # 移除维度为1的维度，将[batch_size, 1]变为[batch_size]
        l = loss(y_hat, y)
        
        # 反向传播和优化
        trainer.zero_grad() # 梯度清零
        l.backward()        # 计算梯度
        trainer.step()      # 更新权重
        
        # 累加指标
        with torch.no_grad():  # 在计算评估指标时不需要梯度
            train_loss_sum += l.item() * X.shape[0] # 当前批次的损失值 * 批次大小
            train_mae_sum += torch.abs(y_hat - y).sum().item()
            train_se_sum += ((y_hat - y) ** 2).sum().item()
            train_total_count += y.shape[0]
            #print('train_total_count: ', train_total_count)

    # 计算当前epoch的平均指标
    with torch.no_grad():  # 在计算平均指标时不需要梯度
        avg_train_loss = train_loss_sum / train_total_count
        avg_train_mae = train_mae_sum / train_total_count
        #avg_train_rmse = np.sqrt((train_se_sum / train_total_count))
        avg_train_rmse = torch.sqrt(torch.tensor(train_se_sum / train_total_count))
        history['train_loss'].append(avg_train_loss)
        history['train_mae'].append(avg_train_mae)
        history['train_rmse'].append(avg_train_rmse.item())

    # --- 测试阶段 (如果提供了test_iter) ---
    avg_test_loss, avg_test_mae = float('nan'), float('nan') # 默认为NaN
    if test_iter:
        net.eval() # 设置为评估模式
        test_loss_sum, test_mae_sum, test_se_sum, test_total_count = 0.0, 0.0, 0.0, 0
        with torch.no_grad(): # 在评估时，不计算梯度
            for X, y in test_iter:
                X, y = X.to(device), y.to(device)
                y_hat = net(X)
                y_hat = y_hat.squeeze() # 移除维度为1的维度，将[batch_size, 1]变为[batch_size]
                l_test = loss(y_hat, y)
                
                test_loss_sum += l_test.item() * X.shape[0]
                test_mae_sum += torch.abs(y_hat - y).sum().item()
                test_se_sum += ((y_hat - y) ** 2).sum().item()
                test_total_count += y.shape[0]
        
        avg_test_loss = test_loss_sum / test_total_count
        avg_test_mae = test_mae_sum / test_total_count
        avg_test_rmse = torch.sqrt(torch.tensor(test_se_sum / test_total_count))
        
        history['test_loss'].append(avg_test_loss)
        history['test_mae'].append(avg_test_mae)
        history['test_rmse'].append(avg_test_rmse.item())
    else:
        # 如果没有测试集，也填充NaN以保持数据对齐
        history['test_loss'].append(avg_test_loss)
        history['test_mae'].append(avg_test_mae)
        history['test_rmse'].append(float('nan'))

    epoch_end_time = time.time()
    epoch_duration = epoch_end_time - epoch_start_time
    
    # --- 汇报与日志记录 ---
    log_entry = (
        f"{epoch + 1:5d} | {epoch_duration:14.2f}s | "
        f"{avg_train_loss:10.6f} | {avg_test_loss:10.6f} | "
        f"{avg_train_mae:10.6f} | {avg_train_rmse:10.6f} | "
        f"{avg_test_mae:10.6f} | {avg_test_rmse:10.6f}" 
    ).replace("nan", "N/A".rjust(10)) # 美化输出

    print(log_entry)
    with open(LOG_FILE_PATH, 'a') as f:
        f.write(log_entry + '\n')

    # --- 保存模型 Checkpoint ---
    if (epoch + 1) % SAVE_EVERY_Q_EPOCHS == 0 or (epoch + 1) == NUM_EPOCHS:
        os.makedirs(MODEL_SAVE_DIR, exist_ok=True)
        save_path = os.path.join(MODEL_SAVE_DIR, f'checkpoint_epoch_{epoch+1}.pth')
        
        # 将所有需要恢复的状态打包进一个字典
        checkpoint_data = {
            'epoch': epoch + 1,
            'model_state_dict': net.state_dict(),
            'optimizer_state_dict': trainer.state_dict(),
            'history': history
        }
        torch.save(checkpoint_data, save_path)

print(f"Checkpoint saved: Full training state saved to {save_path}")
print("\nTraining finished.")


