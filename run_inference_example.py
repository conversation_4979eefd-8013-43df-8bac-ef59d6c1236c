#!/usr/bin/env python3
"""
推理脚本使用示例

这个脚本展示了如何使用 inference.py 进行模型推理
"""

import subprocess
import sys
import os

def run_inference_example():
    """
    运行推理示例
    """
    
    # 示例参数
    checkpoint_path = "./saved_models/checkpoint_epoch_5.pth"  # 使用最新的检查点
    test_dataset_dir = "~/my_private_on_AT1/ak-score-1-data/core-set_junsu-pdbqt/"  # 测试数据集目录
    output_dir = "./inference_results"  # 输出目录
    batch_size = 96  # 批次大小
    num_workers = 8  # 数据加载工作进程数
    
    # 检查检查点文件是否存在
    if not os.path.exists(checkpoint_path):
        print(f"错误：检查点文件不存在: {checkpoint_path}")
        print("可用的检查点文件:")
        saved_models_dir = "./saved_models"
        if os.path.exists(saved_models_dir):
            for file in os.listdir(saved_models_dir):
                if file.endswith('.pth'):
                    print(f"  - {os.path.join(saved_models_dir, file)}")
        return False
    
    # 构建命令
    cmd = [
        sys.executable, "inference.py",
        "--checkpoint", checkpoint_path,
        "--test_dataset_dir", test_dataset_dir,
        "--output_dir", output_dir,
        "--batch_size", str(batch_size),
        "--num_workers", str(num_workers)
    ]
    
    print("运行推理命令:")
    print(" ".join(cmd))
    print("\n" + "="*50)
    
    try:
        # 运行推理脚本
        result = subprocess.run(cmd, check=True, capture_output=False)
        print("\n" + "="*50)
        print("推理完成！")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"\n推理过程中出现错误: {e}")
        return False
    except FileNotFoundError:
        print("\n错误：找不到 inference.py 文件")
        return False


def show_usage():
    """
    显示使用说明
    """
    print("="*60)
    print("模型推理脚本使用说明")
    print("="*60)
    print()
    print("1. 直接运行推理（使用默认参数）:")
    print("   python run_inference_example.py")
    print()
    print("2. 手动运行推理（自定义参数）:")
    print("   python inference.py \\")
    print("     --checkpoint ./saved_models/checkpoint_epoch_XX.pth \\")
    print("     --test_dataset_dir ~/path/to/test/dataset/ \\")
    print("     --output_dir ./inference_results \\")
    print("     --batch_size 96 \\")
    print("     --num_workers 8")
    print()
    print("3. 参数说明:")
    print("   --checkpoint: 模型检查点文件路径（必需）")
    print("   --test_dataset_dir: 测试数据集目录，包含labels.csv（必需）")
    print("   --output_dir: 结果输出目录（可选，默认: ./inference_results）")
    print("   --batch_size: 推理批次大小（可选，默认: 96）")
    print("   --num_workers: 数据加载工作进程数（可选，默认: 8）")
    print()
    print("4. 输出文件:")
    print("   - predictions.txt: 真实值和预测值对比")
    print("   - evaluation_metrics.txt: 评估指标（MAE, RMSE, Pearson相关系数）")
    print()
    print("="*60)


if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] in ["-h", "--help", "help"]:
        show_usage()
    else:
        print("开始运行推理示例...")
        success = run_inference_example()
        
        if success:
            print("\n推理示例运行成功！")
            print("请查看 ./inference_results/ 目录中的结果文件。")
        else:
            print("\n推理示例运行失败。")
            print("请检查错误信息并确保所有依赖文件都存在。")
            print("\n使用 'python run_inference_example.py --help' 查看详细使用说明。")
